{
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-Os",
            "-std=c++17",
            [[-IE:\VulkanSDK\Include]],
            "-fexceptions",
            "-O3",
            "-ffast-math",
            "-ftree-vectorize",
            "-funroll-loops",
            "-fomit-frame-pointer",
            "-ffunction-sections",
            "-fdata-sections",
            "-march=native"
        }
    },
    files = {
        [[src\console_utils.cpp]]
    },
    depfiles_format = "gcc",
    depfiles = "console_utils.o: src\\console_utils.cpp src\\console_utils.h src\\console.h  src\\vector.h src\\constants.h\
"
}