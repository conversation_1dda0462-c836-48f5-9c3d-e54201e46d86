#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <deque>
#include "console.h"
#include "vector.h"

// Enum for different UI elements/windows
enum class UIElementType {
    DEBUG_STATS,      // FPS, position, direction, etc.
    MESSAGE_LOG,      // Game messages/events
    CUSTOM_TEXT,      // For any custom text display
    STATUS_BAR        // DOOM-style status bar at bottom
};

// Struct for UI element position and size
struct UIElement {
    UIElementType type;
    int x, y;           // Top-left position
    int width, height;  // Dimensions
    bool visible;       // Whether this element is visible
    
    UIElement(UIElementType t, int _x, int _y, int w, int h, bool vis = true)
        : type(t), x(_x), y(_y), width(w), height(h), visible(vis) {}
};

// Class representing a message in the log
struct UIMessage {
    std::string text;
    RGBColor color;
    std::chrono::steady_clock::time_point timestamp;
    
    UIMessage(const std::string& msg, const RGBColor& col = RGBColor{200, 200, 200})
        : text(msg), color(col), timestamp(std::chrono::steady_clock::now()) {}
        
    // Comparison operator for finding messages in the queue
    bool operator==(const UIMessage& other) const {
        return text == other.text && 
               color == other.color && 
               std::chrono::duration_cast<std::chrono::milliseconds>(timestamp - other.timestamp).count() == 0;
    }
};

// Main UI class to handle all overlay elements
class UI {
private:
    std::vector<UIElement> elements;
    std::deque<UIMessage> message_log;
    static const size_t MAX_MESSAGES = 5;
    std::chrono::steady_clock::time_point last_fps_update;
    double fps_display_value;
    int frame_counter;
    
    // State variables for display
    Vec3 current_player_pos;
    Vec3 current_camera_dir;
    int triangle_count;
    bool background_color_mode;
    bool pixel_mode; // Flag to track whether we're in ASCII or PIXEL mode
    bool global_ui_visible = true; // NEW: Global flag for UI visibility
    
    // For auto-sizing UI elements based on console size
    void recalculateElementPositions(int console_width, int console_height);
    
    // Helper methods for rendering specific UI elements
    void drawBox(std::vector<std::vector<ConsoleCell>>& buffer, int x, int y, int width, int height, bool fill_inner = true);
    void renderDebugStats(std::vector<std::vector<ConsoleCell>>& buffer, const UIElement& element);
    void renderMessageLog(std::vector<std::vector<ConsoleCell>>& buffer, const UIElement& element);
    void renderStatusBar(std::vector<std::vector<ConsoleCell>>& buffer, const UIElement& element);

public:
    UI();
    
    // Add a UI element
    void addElement(UIElementType type, int x, int y, int width, int height, bool visible = true);
    
    // Add a message to the log
    void addMessage(const std::string& message, const RGBColor& color = RGBColor{200, 200, 200});
    
    // Update UI state (called once per frame)
    void update(double deltaTime, const Vec3& player_pos, const Vec3& camera_dir, double fps);
    
    // Set the triangle count for display
    void setTriangleCount(int count) { triangle_count = count; }
    
    // Set the background color mode state
    void setBackgroundColorMode(bool mode) { background_color_mode = mode; }
    
    // Render UI elements to the console buffer
    void render(std::vector<std::vector<ConsoleCell>>& buffer, int console_width, int console_height);
    
    // Toggle visibility of an element type
    void toggleElement(UIElementType type);
    
    // Handle resize events
    void handleResize(int new_width, int new_height);
    
    // Toggle between ASCII and PIXEL rendering modes
    void toggleRenderMode() { pixel_mode = !pixel_mode; }
    
    // Get current render mode
    bool isPixelMode() const { return pixel_mode; }

    // NEW: Toggle global UI visibility
    void toggleVisibility() { global_ui_visible = !global_ui_visible; }

    // NEW: Check if UI is globally visible
    bool isVisible() const { return global_ui_visible; }
}; 