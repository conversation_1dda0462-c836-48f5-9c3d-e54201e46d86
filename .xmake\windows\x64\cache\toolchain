{
    mingw_arch_x64_plat_windows = {
        bindir = [[C:\msys64\mingw64\bin]],
        arch = "x64",
        mingw = [[C:\msys64\mingw64]],
        __global = true,
        __checked = true,
        cross = "x86_64-w64-mingw32-",
        plat = "windows"
    },
    tool_target_RTerminal_windows_x64_cxx = {
        toolchain_info = {
            name = "mingw",
            arch = "x64",
            plat = "windows",
            cachekey = "mingw_arch_x64_plat_windows"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        toolname = "gxx"
    },
    tool_target_RTerminal_windows_x64_sh = {
        toolchain_info = {
            name = "mingw",
            arch = "x64",
            plat = "windows",
            cachekey = "mingw_arch_x64_plat_windows"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        toolname = "gxx"
    },
    tool_target_RTerminal_windows_x64_ld = {
        toolchain_info = {
            name = "mingw",
            arch = "x64",
            plat = "windows",
            cachekey = "mingw_arch_x64_plat_windows"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        toolname = "gxx"
    },
    tool_target_RTerminal_windows_x64_cc = {
        toolchain_info = {
            name = "mingw",
            arch = "x64",
            plat = "windows",
            cachekey = "mingw_arch_x64_plat_windows"
        },
        program = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]],
        toolname = "gcc"
    }
}