{
    values = {
        [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]],
        {
            "-Os",
            "-std=c++17",
            [[-IE:\VulkanSDK\Include]],
            "-fexceptions",
            "-O3",
            "-ffast-math",
            "-ftree-vectorize",
            "-funroll-loops",
            "-fomit-frame-pointer",
            "-ffunction-sections",
            "-fdata-sections",
            "-march=native"
        }
    },
    files = {
        [[src\skybox.cpp]]
    },
    depfiles_format = "gcc",
    depfiles = "skybox.o: src\\skybox.cpp src\\skybox.h src\\vector.h src\\constants.h  src\\ray.h src\\scene.h src\\triangle.h src\\object.h src\\aabb.h  src\\material.h src\\texture.h src\\ui.h src\\console.h src\\light.h  src\\bvh.h src\\noise.h src\\shading.h\
"
}