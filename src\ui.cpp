#include "ui.h"
#include "constants.h" // For UI_BACKGROUND_COLOR potentially, or other constants
#include <iostream>     // For placeholder std::cout
#include <iomanip>      // For std::setw, std::left
#include <sstream>      // For std::stringstream
#include <algorithm> // For std::min

// Define the UI background color if not already globally available and needed here
// const RGBColor UI_BACKGROUND_COLOR{30, 30, 40}; // Example, ensure consistency

UI::UI() : fps_display_value(0.0), frame_counter(0), triangle_count(0), background_color_mode(false), pixel_mode(false), global_ui_visible(true) {
    last_fps_update = std::chrono::steady_clock::now();
    // Initialize default UI elements or leave to addElement
    // Example: addElement(UIElementType::DEBUG_STATS, 0, 0, 30, 5);
    // addElement(UIElementType::MESSAGE_LOG, 0, 5, 50, MAX_MESSAGES + 2);
    // Add initial UI elements
    addElement(UIElementType::DEBUG_STATS, 0, 0, 30, 8); // Added DEBUG_STATS element
    addElement(UIElementType::MESSAGE_LOG, 0, 9, 60, MAX_MESSAGES + 2); // Added MESSAGE_LOG element, adjusted Y position
}

void UI::addElement(UIElementType type, int x, int y, int width, int height, bool visible) {
    elements.emplace_back(type, x, y, width, height, visible);
}

void UI::addMessage(const std::string& message, const RGBColor& color) {
    if (message_log.size() >= MAX_MESSAGES) {
        message_log.pop_front();
    }
    message_log.emplace_back(message, color);
}

void UI::update(double deltaTime, const Vec3& player_pos, const Vec3& camera_dir, double fps) {
    current_player_pos = player_pos;
    current_camera_dir = camera_dir;

    frame_counter++;
    auto now = std::chrono::steady_clock::now();
    double elapsed_since_last_fps_update = std::chrono::duration<double>(now - last_fps_update).count();

    if (elapsed_since_last_fps_update >= 0.5) { // Update FPS display value roughly every 0.5 seconds
        fps_display_value = fps; // Use the direct FPS passed in
        // Or calculate if not passed: fps_display_value = static_cast<double>(frame_counter) / elapsed_since_last_fps_update;
        frame_counter = 0;
        last_fps_update = now;
    }
}

void UI::render(std::vector<std::vector<ConsoleCell>>& buffer, int console_width, int console_height) {
    // The decision of *whether* to call this method is made by the caller (Game class)
    // based on global_ui_visible, loading state, etc.
    // This method itself just draws the visible UI elements if called.

    if (buffer.empty() || buffer[0].empty()) return;

    // Example: Render a border or a simple message (can be removed if replaced by elements)
    // std::string ui_placeholder = "UI System Active";
    // int start_y = 0; // console_height - 1; // Bottom line
    // if (start_y >= 0 && start_y < static_cast<int>(buffer.size())) {
    //     for (int x = 0; x < static_cast<int>(buffer[start_y].size()) && x < static_cast<int>(ui_placeholder.length()); ++x) {
    //         if (x < console_width) { // Check against console_width too
    //              buffer[start_y][x].character = ui_placeholder[x];
    //              buffer[start_y][x].color = RGBColor{200, 200, 0}; // Yellow text
    //              buffer[start_y][x].is_ui = true;
    //         }
    //     }
    // }

    // Call individual renderers based on element type and visibility
    for (const auto& element : elements) {
        if (!element.visible) continue;

        // Ensure element is within buffer bounds before attempting to draw
        if (element.x < 0 || element.y < 0 ||
            element.x + element.width > console_width ||
            element.y + element.height > console_height) {
            // Optionally add a debug message if an element is out of bounds
            // addMessage("UI Element out of bounds: " + std::to_string(static_cast<int>(element.type)), {255,100,0});
            continue;
        }

        switch (element.type) {
            case UIElementType::DEBUG_STATS:
                renderDebugStats(buffer, element);
                break;
            case UIElementType::MESSAGE_LOG:
                renderMessageLog(buffer, element);
                break;
            case UIElementType::STATUS_BAR:
                // renderStatusBar(buffer, element); // Assuming you'll implement this
                break;
            // Handle other types if added
            default:
                break;
        }
    }
}

void UI::toggleElement(UIElementType type) {
    for (auto& element : elements) {
        if (element.type == type) {
            element.visible = !element.visible;
        }
    }
}

void UI::handleResize(int new_width, int new_height) {
    // Placeholder: In a real system, you might recalculate positions/sizes of UI elements.
    // std::cout << "UI: Resized to " << new_width << "x" << new_height << std::endl;
    recalculateElementPositions(new_width, new_height);
}


// --- Private Helper Implementations (Placeholders) ---
void UI::recalculateElementPositions(int console_width, int console_height) {
    // Example: Reposition/resize DEBUG_STATS and MESSAGE_LOG
    // This is highly dependent on your desired UI layout.
    for (auto& element : elements) {
        if (element.type == UIElementType::DEBUG_STATS) {
            element.x = 1;
            element.y = 1;
            element.width = std::min(40, console_width - 2); // Example width
            element.height = std::min(8, console_height - 2); // Example height
        } else if (element.type == UIElementType::MESSAGE_LOG) {
            element.x = 1;
            element.y = std::min(10, console_height / 2); // Example Y pos
            element.width = std::min(60, console_width - 2);
            element.height = std::min(static_cast<int>(MAX_MESSAGES) + 2, console_height - element.y - 1);
        }
        // Add rules for other elements like STATUS_BAR
    }
}

void UI::drawBox(std::vector<std::vector<ConsoleCell>>& buffer, int x, int y, int width, int height, bool fill_inner) {
    if (width <= 0 || height <= 0) return;
    // Ensure drawing within buffer bounds
    int end_x = std::min(x + width, static_cast<int>(buffer[0].size()));
    int end_y = std::min(y + height, static_cast<int>(buffer.size()));
    int start_x = std::max(0, x);
    int start_y = std::max(0, y);
    RGBColor border_color = {100, 100, 100}; // Grey border
    // Ensure UI_BACKGROUND_COLOR is accessible or defined here if needed for fill
    // For now, using a hardcoded dark grey for fill
    RGBColor fill_color = {20, 20, 30};

    for (int r = start_y; r < end_y; ++r) {
        for (int c = start_x; c < end_x; ++c) {
            char box_char = ' ';
            bool is_border_cell = (r == y || r == y + height - 1 || c == x || c == x + width - 1);

            if (is_border_cell) {
                if (r == y && c == x) box_char = '+'; // Top-left
                else if (r == y && c == x + width - 1) box_char = '+'; // Top-right
                else if (r == y + height - 1 && c == x) box_char = '+'; // Bottom-left
                else if (r == y + height - 1 && c == x + width - 1) box_char = '+'; // Bottom-right
                else if (r == y || r == y + height - 1) box_char = '-'; // Horizontal
                else if (c == x || c == x + width - 1) box_char = '|'; // Vertical
            }

            buffer[r][c].character = box_char;
            buffer[r][c].color = is_border_cell ? border_color : (fill_inner ? fill_color : buffer[r][c].color);
            buffer[r][c].is_ui = true; // Mark as UI cell
            buffer[r][c].is_border = is_border_cell; // Set the new is_border flag
        }
    }
}


void UI::renderDebugStats(std::vector<std::vector<ConsoleCell>>& buffer, const UIElement& element) {
    drawBox(buffer, element.x, element.y, element.width, element.height);

    std::stringstream ss;
    ss << std::fixed << std::setprecision(2);
    ss << "FPS: " << fps_display_value;
    std::string fps_str = ss.str();
    ss.str(""); ss.clear(); // Clear stream for next use

    ss << "Pos: " << current_player_pos.x << "," << current_player_pos.y << "," << current_player_pos.z;
    std::string pos_str = ss.str();
    ss.str(""); ss.clear();

    ss << "Dir: " << current_camera_dir.x << "," << current_camera_dir.y << "," << current_camera_dir.z;
    std::string dir_str = ss.str();
    ss.str(""); ss.clear();

    ss << "Tris: " << triangle_count;
    std::string tri_str = ss.str();

    std::string mode_str = pixel_mode ? "Mode: Pixel" : "Mode: ASCII";

    std::vector<std::string> lines = {fps_str, pos_str, dir_str, tri_str, mode_str};

    RGBColor text_color = {200, 200, 200};

    for (size_t i = 0; i < lines.size(); ++i) {
        int line_y = element.y + 1 + i; // +1 for border
        int line_x = element.x + 1;
        if (line_y < element.y + element.height -1) { // Check vertical bounds
            for (size_t j = 0; j < lines[i].length(); ++j) {
                if (line_x + j < element.x + element.width -1) { // Check horizontal bounds
                     if (line_y >= 0 && line_y < buffer.size() && line_x + j >= 0 && line_x +j < buffer[0].size()){
                        buffer[line_y][line_x + j].character = lines[i][j];
                        buffer[line_y][line_x + j].color = text_color;
                        buffer[line_y][line_x + j].is_ui = true;
                        buffer[line_y][line_x + j].is_border = false; // Explicitly set to false for text
                     }
                } else break;
            }
        } else break;
    }
}

void UI::renderMessageLog(std::vector<std::vector<ConsoleCell>>& buffer, const UIElement& element) {
    drawBox(buffer, element.x, element.y, element.width, element.height);

    int max_display_lines = element.height - 2; // Exclude top and bottom borders
    if (max_display_lines <= 0) return;

    // Determine how many messages to display (up to max_display_lines and MAX_MESSAGES)
    size_t num_messages_to_display = std::min({message_log.size(), static_cast<size_t>(max_display_lines), MAX_MESSAGES});

    // Start drawing from the end of the message_log deque
    int current_line_y = element.y + 1; // Start below top border

    // Get the current time to calculate time elapsed for timestamp
    auto now = std::chrono::steady_clock::now();

    // Iterate backwards through the messages to display the latest ones
    for (size_t i = 0; i < num_messages_to_display; ++i) {
        // Access messages from the end of the deque
        const auto& msg = message_log[message_log.size() - num_messages_to_display + i];

        // Calculate time elapsed (in seconds)
        std::chrono::duration<double> elapsed = now - msg.timestamp;
        long long elapsed_seconds = static_cast<long long>(elapsed.count());

        // Format timestamp (simple [SSs] or [MMm SSs] format)
        std::stringstream timestamp_ss;
        timestamp_ss << "[";
        if (elapsed_seconds >= 60) {
            timestamp_ss << elapsed_seconds / 60 << "m ";
        }
        timestamp_ss << elapsed_seconds % 60 << "s] ";
        std::string timestamp_str = timestamp_ss.str();


        std::string text_to_render = timestamp_str + msg.text;

        // Truncate if message is too long for the available width
        int available_width = element.width - 2; // Exclude left and right borders
        if (text_to_render.length() > static_cast<size_t>(available_width)) {
            text_to_render = text_to_render.substr(0, available_width);
        }

        // Render the text
        int current_line_x = element.x + 1; // Start after left border
         if (current_line_y < element.y + element.height - 1) { // Check against bottom border
            for (size_t j = 0; j < text_to_render.length(); ++j) {
                 if (current_line_x + j < element.x + element.width - 1) { // Check against right border
                    if (current_line_y >= 0 && current_line_y < buffer.size() && current_line_x + j >= 0 && current_line_x + j < buffer[0].size()) {
                        buffer[current_line_y][current_line_x + j].character = text_to_render[j];
                        buffer[current_line_y][current_line_x + j].color = msg.color; // Use message color
                        buffer[current_line_y][current_line_x + j].is_ui = true; // Mark as UI cell
                        buffer[current_line_y][current_line_x + j].is_border = false; // Explicitly set to false for text
                    }
                 } else break; // Stop if out of horizontal bounds
            }
         } else break; // Stop if out of vertical bounds

        current_line_y++; // Move to the next line for the next message
    }
}

void UI::renderStatusBar(std::vector<std::vector<ConsoleCell>>& buffer, const UIElement& element) {
    // Placeholder - implement your DOOM-style status bar here
    // This would involve drawing a background, health, ammo, etc.
    // For now, just fill with a color.
    RGBColor status_bar_bg = {50, 50, 70};
    for (int r = element.y; r < element.y + element.height; ++r) {
        if (r >= 0 && r < buffer.size()) {
            for (int c = element.x; c < element.x + element.width; ++c) {
                if (c >= 0 && c < buffer[r].size()) {
                    buffer[r][c].character = ' ';
                    buffer[r][c].color = status_bar_bg; // Or set background if using that mode
                    buffer[r][c].is_ui = true;
                }
            }
        }
    }
    // Example text:
    std::string status_text = "HP:100 AMMO:50/200";
    int text_y = element.y + element.height / 2;
     if (text_y >= 0 && text_y < buffer.size()){
        for(size_t i = 0; i < status_text.length(); ++i) {
            int text_x = element.x + 1 + i;
            if (text_x >=0 && text_x < buffer[text_y].size() && text_x < element.x + element.width -1){
                buffer[text_y][text_x].character = status_text[i];
                buffer[text_y][text_x].color = {200,200,200};
                buffer[text_y][text_x].is_ui = true;
            } else break;
        }
     }
} 