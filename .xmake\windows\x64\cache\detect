{
    ["detect.sdks.find_mingw"] = {
        mingw = {
            cross = "x86_64-w64-mingw32-",
            bindir = [[C:\msys64\mingw64\bin]],
            sdkdir = [[C:\msys64\mingw64]]
        }
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcxx = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--param"] = true,
            ["-time"] = true,
            ["-print-multi-os-directory"] = true,
            ["-S"] = true,
            ["-Xpreprocessor"] = true,
            ["-B"] = true,
            ["--target-help"] = true,
            ["-dumpspecs"] = true,
            ["-pie"] = true,
            ["-x"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-dumpmachine"] = true,
            ["--help"] = true,
            ["-print-multi-directory"] = true,
            ["-print-multiarch"] = true,
            ["-save-temps"] = true,
            ["-dumpversion"] = true,
            ["-shared"] = true,
            ["-print-search-dirs"] = true,
            ["-print-sysroot-headers-suffix"] = true,
            ["-Xassembler"] = true,
            ["-print-sysroot"] = true,
            ["-Xlinker"] = true,
            ["-c"] = true,
            ["-v"] = true,
            ["-print-multi-lib"] = true,
            ["-o"] = true,
            ["-no-canonical-prefixes"] = true,
            ["-pass-exit-codes"] = true,
            ["-E"] = true,
            ["-pipe"] = true,
            ["--version"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-march=native"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-s"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-MMD -MF"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-Os"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-Wno-gnu-line-marker -Werror"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffast-math"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-flto"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdiagnostics-color=always"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ftree-vectorize"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-march=native"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld___-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-ffunction-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-fomit-frame-pointer"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-O3"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx___-fdata-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_ld_ldflags__-Wl,--gc-sections"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-funroll-loops"] = true,
        ["windows_x64_C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0_cxx_cxflags__-fdata-sections"] = true
    },
    find_program = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program_mingw_arch_x64_plat_windows_checktoolsh = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    find_program_mingw_arch_x64_plat_windows_checktoolcc = {
        ["x86_64-w64-mingw32-gcc"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-gcc]]
    },
    find_programver = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++"] = "15.1.0"
    },
    find_program_mingw_arch_x64_plat_windows_checktoolld = {
        ["x86_64-w64-mingw32-g++"] = [[C:\msys64\mingw64\bin\x86_64-w64-mingw32-g++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++_15.1.0"] = {
            ["--dynamic-list"] = true,
            ["--remap-inputs"] = true,
            ["--default-image-base-high"] = true,
            ["--version-exports-section"] = true,
            ["--allow-shlib-undefined"] = true,
            ["--pic-executable"] = true,
            ["--allow-multiple-definition"] = true,
            ["--out-implib"] = true,
            ["--discard-all"] = true,
            ["-h"] = true,
            ["-O"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--error-handling-script"] = true,
            ["--disable-linker-version"] = true,
            ["--gpsize"] = true,
            ["-soname"] = true,
            ["--format"] = true,
            ["--cref"] = true,
            ["--filter"] = true,
            ["-Bsymbolic-functions"] = true,
            ["-Tbss"] = true,
            ["-flto"] = true,
            ["--split-by-file"] = true,
            ["--enable-linker-version"] = true,
            ["--start-group"] = true,
            ["--no-map-whole-files"] = true,
            ["--disable-long-section-names"] = true,
            ["-dp"] = true,
            ["-Qy"] = true,
            ["--no-allow-shlib-undefined"] = true,
            ["--warn-common"] = true,
            ["--stats"] = true,
            ["--no-check-sections"] = true,
            ["-e"] = true,
            ["-o"] = true,
            ["-debug"] = true,
            ["--no-warn-rwx-segments"] = true,
            ["--nmagic"] = true,
            ["--large-address-aware"] = true,
            ["--print-gc-sections"] = true,
            ["--enable-non-contiguous-regions-warnings"] = true,
            ["--trace"] = true,
            ["-Ttext"] = true,
            ["--no-whole-archive"] = true,
            ["--enable-extra-pep-debug"] = true,
            ["--accept-unknown-input-arch"] = true,
            ["--gc-keep-exported"] = true,
            ["--remap-inputs-file"] = true,
            ["--demangle"] = true,
            ["--push-state"] = true,
            ["--no-undefined"] = true,
            ["--force-group-allocation"] = true,
            ["-plugin"] = true,
            ["--no-relax"] = true,
            ["--output-def"] = true,
            ["--warn-unresolved-symbols"] = true,
            ["-Bno-symbolic"] = true,
            ["--no-warnings"] = true,
            ["-EB"] = true,
            ["--sort-section"] = true,
            ["-f"] = true,
            ["--subsystem"] = true,
            ["--no-print-map-discarded"] = true,
            ["--export-dynamic-symbol-list"] = true,
            ["-EL"] = true,
            ["-u"] = true,
            ["--enable-auto-image-base"] = true,
            ["--disable-auto-import"] = true,
            ["--discard-locals"] = true,
            ["--entry"] = true,
            ["--no-omagic"] = true,
            ["--no-as-needed"] = true,
            ["-V"] = true,
            ["--kill-at"] = true,
            ["--add-stdcall-alias"] = true,
            ["--no-warn-mismatch"] = true,
            ["--undefined"] = true,
            ["-nostdlib"] = true,
            ["--no-ctf-variables"] = true,
            ["--library"] = true,
            ["--library-path"] = true,
            ["--orphan-handling"] = true,
            ["-Map"] = true,
            ["-plugin-save-temps"] = true,
            ["--default-imported-symver"] = true,
            ["--minor-image-version"] = true,
            ["--sort-common"] = true,
            ["--warn-alternate-em"] = true,
            ["--undefined-version"] = true,
            ["-no-pie"] = true,
            ["--no-strip-discarded"] = true,
            ["--ignore-unresolved-symbol"] = true,
            ["--print-map"] = true,
            ["--omagic"] = true,
            ["--emit-relocs"] = true,
            ["--no-define-common"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["-A"] = true,
            ["--no-dynamic-linker"] = true,
            ["-m"] = true,
            ["-F"] = true,
            ["--major-os-version"] = true,
            ["--export-all-symbols"] = true,
            ["--dynamic-linker"] = true,
            ["--enable-auto-import"] = true,
            ["--trace-symbol"] = true,
            ["--section-alignment"] = true,
            ["-dT"] = true,
            ["--whole-archive"] = true,
            ["--dynamic-list-cpp-typeinfo"] = true,
            ["--architecture"] = true,
            ["--wrap"] = true,
            ["--dll"] = true,
            ["--ctf-variables"] = true,
            ["--section-ordering-file"] = true,
            ["--strip-all"] = true,
            ["--no-undefined-version"] = true,
            ["-init"] = true,
            ["--relax"] = true,
            ["--as-needed"] = true,
            ["--enable-long-section-names"] = true,
            ["--warn-textrel"] = true,
            ["--no-fatal-warnings"] = true,
            ["--strip-discarded"] = true,
            ["--verbose"] = true,
            ["--minor-subsystem-version"] = true,
            ["-assert"] = true,
            ["--no-accept-unknown-input-arch"] = true,
            ["-l"] = true,
            ["--disable-reloc-section"] = true,
            ["--no-demangle"] = true,
            ["--spare-dynamic-tags"] = true,
            ["--dynamic-list-cpp-new"] = true,
            ["--force-exe-suffix"] = true,
            ["--check-sections"] = true,
            ["-Bshareable"] = true,
            ["--require-defined"] = true,
            ["--dependency-file"] = true,
            ["-T"] = true,
            ["-G"] = true,
            ["--fatal-warnings"] = true,
            ["--enable-reloc-section"] = true,
            ["--target-help"] = true,
            ["-rpath-link"] = true,
            ["--discard-none"] = true,
            ["-y"] = true,
            ["--reduce-memory-overheads"] = true,
            ["--warn-multiple-gp"] = true,
            ["--disable-auto-image-base"] = true,
            ["--file-alignment"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--version-script"] = true,
            ["--major-subsystem-version"] = true,
            ["--enable-extra-pe-debug"] = true,
            ["--major-image-version"] = true,
            ["--print-map-locals"] = true,
            ["-qmagic"] = true,
            ["--disable-large-address-aware"] = true,
            ["--exclude-modules-for-implib"] = true,
            ["-c"] = true,
            ["--gc-sections"] = true,
            ["--auxiliary"] = true,
            ["--default-symver"] = true,
            ["--defsym"] = true,
            ["-Bsymbolic"] = true,
            ["--no-keep-memory"] = true,
            ["--warn-section-align"] = true,
            ["--just-symbols"] = true,
            ["--traditional-format"] = true,
            ["--default-image-base-low"] = true,
            ["--support-old-code"] = true,
            ["--copy-dt-needed-entries"] = true,
            ["-fini"] = true,
            ["--no-warn-search-mismatch"] = true,
            ["-Tdata"] = true,
            ["--no-export-dynamic"] = true,
            ["--print-map-discarded"] = true,
            ["--compat-implib"] = true,
            ["--exclude-symbols"] = true,
            ["--exclude-libs"] = true,
            ["-a"] = true,
            ["-rpath"] = true,
            ["--minor-os-version"] = true,
            ["--error-unresolved-symbols"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--dynamic-list-data"] = true,
            ["-L"] = true,
            ["--no-copy-dt-needed-entries"] = true,
            ["--pop-state"] = true,
            ["--help"] = true,
            ["-Tldata-segment"] = true,
            ["-Ttext-segment"] = true,
            ["--no-gc-sections"] = true,
            ["--output"] = true,
            ["-I"] = true,
            ["--strip-debug"] = true,
            ["--warn-once"] = true,
            ["--exclude-all-symbols"] = true,
            ["--print-sysroot"] = true,
            ["--version"] = true,
            ["-Y"] = true,
            ["--oformat"] = true,
            ["--split-by-reloc"] = true,
            ["--retain-symbols-file"] = true,
            ["--print-memory-usage"] = true,
            ["--export-dynamic-symbol"] = true,
            ["-g"] = true,
            ["--warn-duplicate-exports"] = true,
            ["--unique"] = true,
            ["--relocatable"] = true,
            ["--mri-script"] = true,
            ["--default-script"] = true,
            ["--map-whole-files"] = true,
            ["-b"] = true,
            ["--stack"] = true,
            ["--export-dynamic"] = true,
            ["-Trodata-segment"] = true,
            ["-R"] = true,
            ["--no-print-map-locals"] = true,
            ["-static"] = true,
            ["--enable-non-contiguous-regions"] = true,
            ["--heap"] = true,
            ["--print-output-format"] = true,
            ["--end-group"] = true,
            ["-plugin-opt"] = true,
            ["--task-link"] = true,
            ["--script"] = true,
            ["--disable-multiple-abs-defs"] = true,
            ["--image-base"] = true,
            ["--no-print-gc-sections"] = true,
            ["--section-start"] = true,
            ["-Ur"] = true
        }
    }
}