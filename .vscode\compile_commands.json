[{"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\main.cpp.obj", "main.cpp"], "file": "main.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\console_utils.cpp.obj", "src\\console_utils.cpp"], "file": "src\\console_utils.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\input.cpp.obj", "src\\input.cpp"], "file": "src\\input.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\player.cpp.obj", "src\\player.cpp"], "file": "src\\player.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\renderer.cpp.obj", "src\\renderer.cpp"], "file": "src\\renderer.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\shading.cpp.obj", "src\\shading.cpp"], "file": "src\\shading.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\skybox.cpp.obj", "src\\skybox.cpp"], "file": "src\\skybox.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\software_backend.cpp.obj", "src\\software_backend.cpp"], "file": "src\\software_backend.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\texture.cpp.obj", "src\\texture.cpp"], "file": "src\\texture.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\ui.cpp.obj", "src\\ui.cpp"], "file": "src\\ui.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\vulkan_backend.cpp.obj", "src\\vulkan_backend.cpp"], "file": "src\\vulkan_backend.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\vulkan_renderer.cpp.obj", "src\\vulkan_renderer.cpp"], "file": "src\\vulkan_renderer.cpp"}, {"directory": "c:\\Users\\<USER>\\Desktop\\RTerminal", "arguments": ["C:\\msys64\\mingw64\\bin\\x86_64-w64-mingw32-g++.exe", "-c", "-<PERSON><PERSON>", "-std=c++17", "-IE:\\VulkanSDK\\Include", "-fexceptions", "-O3", "-ffast-math", "-ftree-vectorize", "-funroll-loops", "-fomit-frame-pointer", "-ffunction-sections", "-fdata-sections", "-march=native", "-o", "build\\.objs\\RTerminal\\windows\\x64\\release\\src\\vulkan_resources.cpp.obj", "src\\vulkan_resources.cpp"], "file": "src\\vulkan_resources.cpp"}]